name: CI Lint & Build
on:
  pull_request:
    branches:
      - '**'

jobs:
  lint-and-build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node and pnpm
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'

      - name: Install pnpm globally
        run: npm install -g pnpm@10.11.0

      - name: Verify pnpm version
        run: pnpm --version

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: linting
        run: pnpm eslint "src/**/*.{ts,tsx}" --fix

      - name: Run TypeScript check
        run: pnpm type-check

      - name: Run Prettier code formatter
        run: pnpm format:fix

      - name: Build the application
        run: pnpm build
