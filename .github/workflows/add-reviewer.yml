name: add-reviewer.yml
on:
  pull_request:
    types: [ opened, reopened, ready_for_review, edited, synchronize ]

jobs:
  add-reviewer:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Add Copilot and project owner as reviewers
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.REVIEWER_PAT }}
          script: |
            github.rest.pulls.requestReviewers({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              reviewers: ['github-copilot', 'Alaedd1ne']
            })