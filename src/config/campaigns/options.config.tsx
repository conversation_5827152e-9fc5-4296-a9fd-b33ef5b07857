const presetColors = [
  '#1E90FF',
  '#87CEEB',
  '#FFD700',
  '#F5DEB3',
  '#32CD32',
  '#98FB98',
  '#DC143C',
  '#FFB6C1',
  '#3F51B5',
];

const marketingObjectivesOptions = [
  { value: 'brand awareness', label: 'Brand Awareness' },
  { value: 'audience engagement', label: 'Audience Engagement' },
  { value: 'lead generation', label: 'Lead Generation' },
  { value: 'driving website traffic', label: 'Driving Website Traffic' },
  { value: 'conversion', label: 'Conversion (Sales or Sign-ups)' },
  {
    value: 'customer retention and loyalty',
    label: 'Customer Retention and Loyalty',
  },
  { value: 'reputation management', label: 'Reputation Management' },
  { value: 'thought leadership', label: 'Thought Leadership' },
  { value: 'community building', label: 'Community Building' },
  { value: 'event promotion', label: 'Event Promotion' },
  { value: 'improving customer service', label: 'Improving Customer Service' },
  { value: 'product discovery', label: 'Product Discovery' },
  { value: 'sales and promotions', label: 'Sales and Promotions' },
  { value: 'showcasing product usage', label: 'Showcasing Product Usage' },
  { value: 'social proof and reviews', label: 'Social Proof and Reviews' },
  { value: 'direct conversions', label: 'Direct Conversions (Sales)' },
  { value: 'abandoned cart recovery', label: 'Abandoned Cart Recovery' },
  { value: 'seasonal campaigns', label: 'Seasonal Campaigns' },
  {
    value: 'upselling and cross-selling',
    label: 'Upselling and Cross-Selling',
  },
  { value: 'customer education', label: 'Customer Education' },
  {
    value: 'building customer trust and loyalty',
    label: 'Building Customer Trust and Loyalty',
  },
  { value: 'creating urgency', label: 'Creating Urgency (FOMO)' },
  {
    value: 'collaborations and influencer partnerships',
    label: 'Collaborations and Influencer Partnerships',
  },
  {
    value: 'encouraging repeat purchases',
    label: 'Encouraging Repeat Purchases',
  },
  {
    value: 'visual merchandising on social media',
    label: 'Visual Merchandising on Social Media',
  },
  {
    value: 'pre-orders and product launches',
    label: 'Pre-orders and Product Launches',
  },
];

const periodOptions = [
  { value: '1 week', label: '1 Week' },
  { value: '2 week', label: '2 Week' },
  { value: '1 month', label: '1 Month' },
];

const frequencyOptions = [
  {
    value: 'Daily',
    label: 'Daily',
  },
  {
    value: 'Alternate Days',
    label: 'Alternate Days',
  },
  {
    value: 'Weekly',
    label: 'Weekly',
  },
  {
    value: 'Bi-Weekly',
    label: 'Bi Weekly',
  },
  {
    value: 'Monthly',
    label: 'Monthly',
  },
  {
    value: 'Seasonal/Occasional',
    label: 'Seasonal / Occasional',
  },
  {
    value: 'Event-Based',
    label: 'Event Based',
  },
];

const regionsAndCountriesOptions = [
  {
    groupLabel: 'Regions',
    items: [
      { value: 'north-america', label: 'North America', icon: '🌎' },
      { value: 'south-america', label: 'South America', icon: '🌎' },
      { value: 'europe', label: 'Europe', icon: '🌍' },
      { value: 'asia', label: 'Asia', icon: '🌏' },
      { value: 'africa', label: 'Africa', icon: '🌍' },
      { value: 'oceania', label: 'Oceania', icon: '🌏' },
    ],
  },
  {
    groupLabel: 'Countries',
    items: [
      { value: 'united-states', label: 'United States', icon: '🇺🇸' },
      { value: 'canada', label: 'Canada', icon: '🇨🇦' },
      { value: 'mexico', label: 'Mexico', icon: '🇲🇽' },
      { value: 'united-kingdom', label: 'United Kingdom', icon: '🇬🇧' },
      { value: 'france', label: 'France', icon: '🇫🇷' },
      { value: 'germany', label: 'Germany', icon: '🇩🇪' },
      { value: 'china', label: 'China', icon: '🇨🇳' },
      { value: 'japan', label: 'Japan', icon: '🇯🇵' },
      { value: 'india', label: 'India', icon: '🇮🇳' },
      { value: 'australia', label: 'Australia', icon: '🇦🇺' },
      { value: 'brazil', label: 'Brazil', icon: '🇧🇷' },
      { value: 'south-africa', label: 'South Africa', icon: '🇿🇦' },
    ],
  },
];

export function useOptionsConfig() {
  return {
    presetColors,
    marketingObjectivesOptions,
    periodOptions,
    frequencyOptions,
    regionsAndCountriesOptions,
  };
}
