import { max, isAfter, isBefore, startOfToday } from 'date-fns';
import React from 'react';

import { useAllKnowledge } from '@/api/hooks/knowledge/queries';
import { useCurrentUserStore } from '@/stores/current-user-store';

import { simpleCreateCampaignFormOpts } from '@/config/campaigns/form.config';
import { useOptionsConfig } from '@/config/campaigns/options.config';

import { withForm } from '@/components/shared/form';
import { MultiSelect } from '@/components/shared/form/multi-select';
import { Label } from '@/components/ui/label';

export const NewCampaignForm = withForm({
  ...simpleCreateCampaignFormOpts,
  defaultValues: {
    ...simpleCreateCampaignFormOpts.defaultValues,
  },
  render: function Render({ form }) {
    const { presetColors, marketingObjectivesOptions } = useOptionsConfig();

    const currentUser = useCurrentUserStore().getUser();

    const { data: knowledges } = useAllKnowledge(
      currentUser?.companyId,
      currentUser?.brandId
    );

    return (
      <form
        className='grid gap-3'
        onSubmit={async (e) => {
          e.preventDefault();
          e.stopPropagation();
          await form.handleSubmit();
        }}
      >
        <form.AppField name='Platforms'>
          {(field) => (
            <field.SocialMediaRadioField
              label='Choose targeted social media'
              multiSelect
            />
          )}
        </form.AppField>

        <form.AppField name='Name'>
          {(field) => <field.TextField label='Title' placeholder='content' />}
        </form.AppField>

        <form.AppField name='Description'>
          {(field) => (
            <field.TextareaField
              label='Description'
              placeholder='content'
              className='h-28'
            />
          )}
        </form.AppField>

        <form.AppField name='Color'>
          {(field) => (
            <field.ColorPickerField
              required={false}
              label='Define a color for this campaign'
              defaultPresets={presetColors}
            />
          )}
        </form.AppField>

        <form.AppField name='Objective'>
          {(field) => (
            <field.SelectField
              required={false}
              label='Objective'
              placeholder='Select an objective'
              options={marketingObjectivesOptions}
            />
          )}
        </form.AppField>

        <form.AppField name='Knowledges'>
          {(field) => {
            return (
              <>
                <Label htmlFor={field.name}>Knowledge</Label>
                <MultiSelect
                  options={
                    knowledges?.map((knowledge) => ({
                      label: knowledge.Name,
                      value: knowledge.Id,
                    })) ?? []
                  }
                  onValueChange={(selectedOptions) => {
                    field.handleChange(selectedOptions.map((option) => option));
                  }}
                  placeholder='Select knowledges'
                />
              </>
            );
          }}
        </form.AppField>

        <form.Subscribe
          selector={(state) => ({
            startDate: state.values.StartDate,
            endDate: state.values.EndDate,
          })}
        >
          {({ startDate, endDate }) => (
            <>
              <form.AppField
                name='StartDate'
                listeners={{
                  onChange: ({ value }) => {
                    if (value && endDate && isAfter(value, endDate)) {
                      form.setFieldValue('EndDate', undefined);
                    }
                  },
                }}
              >
                {(field) => (
                  <field.DatePickerField
                    label='Start Date'
                    required={false}
                    datePickerProps={{
                      disabled: (date) => isBefore(date, startOfToday()),
                    }}
                  />
                )}
              </form.AppField>

              <form.AppField name='EndDate'>
                {(field) => (
                  <field.DatePickerField
                    label='End Date'
                    required={false}
                    datePickerProps={{
                      disabled: (date) => {
                        const minAllowed = startDate
                          ? max([startDate, startOfToday()])
                          : startOfToday();
                        return isBefore(date, minAllowed);
                      },
                    }}
                  />
                )}
              </form.AppField>
            </>
          )}
        </form.Subscribe>
      </form>
    );
  },
});
