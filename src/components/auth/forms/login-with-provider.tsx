'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { useProviderSignIn } from '@/api/hooks/auth/mutations';

import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';

export function LoginWithProvider() {
  const t = useTranslations();
  const { handleProviderSignIn } = useProviderSignIn();

  return (
    <div className='grid grid-cols-3 gap-4'>
      <Button
        variant='outline'
        className='rounded-lg'
        onClick={async () => await handleProviderSignIn('google')}
      >
        <Icons.googleOfficial />
        <span className='text-xs font-light'>{t('google')}</span>
      </Button>
      <Button
        variant='outline'
        className='rounded-lg'
        onClick={async () => await handleProviderSignIn('microsoft')}
      >
        <Icons.microsoftOfficial />
        <span className='text-xs font-light'>{t('microsoft')}</span>
      </Button>

      <Button
        variant='outline'
        className='rounded-lg'
        onClick={async () => await handleProviderSignIn('linkedin')}
      >
        <Icons.linkedinOfficial />
        <span className='text-xs font-light'>{t('linkedin')}</span>
      </Button>
    </div>
  );
}
