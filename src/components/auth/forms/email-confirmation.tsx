'use client';

import { sendPasswordResetEmail } from 'firebase/auth';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

import { auth } from '@/firebase/config';
import { Link } from '@/i18n/routing';

import { Button } from '@/components/ui/button';

export default function EmailConfirmation() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email');

  const handleResend = async () => {
    if (
      !email ||
      typeof email !== 'string' ||
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
    ) {
      toast.error(t('email_confirm.toast_error_email'));
      return;
    }

    try {
      await sendPasswordResetEmail(auth, email, {
        url: `${window.location.origin}/new-password`, // URL de redirection après réinitialisation
        handleCodeInApp: true,
      });

      toast.success(t('email_confirm.toast_success'));
    } catch (error: unknown) {
      if (error instanceof Error) {
        return toast.error(t('email_confirm.toast_error'));
      }
    }
  };
  const t = useTranslations();
  return (
    <div className='w-1/2 rounded-lg bg-background px-2 py-6 shadow-md'>
      <h1 className='text-2xl font-bold'>{t('email_confirm.title')}</h1>

      <div className='text-gray-600'>
        <span>{t('email_confirm.subtitle')}</span>
        <span className='block font-medium text-blue-600'>
          {email || t('email_confirm.email')}
        </span>
        <span>{t('email_confirm.subtitle_')}</span>
      </div>

      <div className='mt-6'>
        <Button
          onClick={handleResend}
          className='font-medium text-blue-600 hover:text-blue-800'
          variant='ghost'
        >
          {t('email_confirm.button_resend')}
        </Button>
      </div>

      <Link
        href='/login'
        className='mt-8 text-sm text-gray-600 hover:text-gray-800'
      >
        {t('email_confirm.button_back')}
      </Link>
    </div>
  );
}
