'use client';

import { useTranslations } from 'next-intl';
import React from 'react';
import { toast } from 'sonner';

import { useLogin } from '@/api/hooks/auth/mutations';
import { loginSchema } from '@/api/models/schemas/login.schema';
import { Link, useRouter } from '@/i18n/routing';

import { useAppForm } from '@/components/shared/form';

export default function LoginForm() {
  const t = useTranslations();
  const router = useRouter();
  const { mutateAsync } = useLogin();

  const { AppForm, AppField, SubmitButton, handleSubmit, reset } = useAppForm({
    defaultValues: {
      UserEmail: '',
      UserPassword: '',
      rememberMe: false,
    },
    validators: {
      onChange: loginSchema(t),
    },
    onSubmit: async ({ value }) => {
      try {
        await mutateAsync(value);
        toast.success(t('form.success'));

        router.push('/dashboard');
        reset();
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error(t(error.message));
        }
      }
    },
    onSubmitInvalid: () => {
      toast.error(t('form.invalid_form'));
    },
  });

  return (
    <form
      className='flex flex-col gap-4'
      onSubmit={async (e) => {
        e.preventDefault();
        await handleSubmit();
      }}
    >
      <div className='flex flex-col gap-2'>
        <h1 className='text-2xl font-semibold text-foreground/80'>
          {t('title')}
        </h1>
      </div>

      <div className='grid gap-4'>
        <AppField name='UserEmail'>
          {(field) => (
            <field.TextField
              type='email'
              label={t('form.email')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='email'
              placeholder='<EMAIL>'
            />
          )}
        </AppField>
        <AppField name='UserPassword'>
          {(field) => (
            <field.TextField
              type='password'
              label={t('form.password')}
              labelClassName='text-xs font-normal text-foreground/70'
              autoComplete='current-password'
              placeholder='********'
            />
          )}
        </AppField>
        <div className='flex items-center justify-between'>
          <AppField name='rememberMe'>
            {(field) => (
              <field.CheckboxField
                label={t('remember_me')}
                required={false}
                labelClassName='text-xs font-normal text-foreground/70'
                checkboxClassName='rounded-md'
              />
            )}
          </AppField>

          <Link
            href='/new-password'
            className='text-sm font-medium text-[#A1A1AA]'
          >
            {t('form.forgot_password')}
          </Link>
        </div>
      </div>
      <AppForm>
        <SubmitButton>{t('login')}</SubmitButton>
      </AppForm>
    </form>
  );
}
