import React from 'react';

import Airtable from '@/assets/icons/airtable.svg';
import ArrowRight from '@/assets/icons/arrow-right.svg';
import Base from '@/assets/icons/base.svg';
import Bookmark from '@/assets/icons/bookmark.svg';
import X from '@/assets/icons/brand-x.svg';
import CalendarAdd from '@/assets/icons/calendar-add.svg';
import CalendarInput from '@/assets/icons/calendar-input.svg';
import Calendar from '@/assets/icons/calendar.svg';
import CirclePlay from '@/assets/icons/circle-play.svg';
import CloudCheck from '@/assets/icons/cloud-check.svg';
import CloudUpload from '@/assets/icons/cloud-upload.svg';
import Crown from '@/assets/icons/crown.svg';
import Drive from '@/assets/icons/drive.svg';
import Edit from '@/assets/icons/edit.svg';
import EmojiIcon from '@/assets/icons/emoji-icon.svg';
import Eye from '@/assets/icons/eye.svg';
import FacebookChat from '@/assets/icons/facebook-chat.svg';
import Facebook<PERSON>ike from '@/assets/icons/facebook-like.svg';
import FacebookOfficial from '@/assets/icons/facebook-official.svg';
import FacebookShare from '@/assets/icons/facebook-share.svg';
import Facebook from '@/assets/icons/facebook.svg';
import Filter from '@/assets/icons/filter.svg';
import FolderOpen from '@/assets/icons/folder-open.svg';
import GalleryEdit from '@/assets/icons/gallery-edit.svg';
import GalleryHorizontal from '@/assets/icons/gallery-horizontal.svg';
import GlobalScale from '@/assets/icons/global-scale.svg';
import GoogleDrive from '@/assets/icons/google-drive.svg';
import GoogleOfficial from '@/assets/icons/google-official.svg';
import Hashtag from '@/assets/icons/hastag.svg';
import Headset from '@/assets/icons/headset.svg';
import Heart from '@/assets/icons/heart.svg';
import InfoCircle from '@/assets/icons/info-circle.svg';
import InstagramOfficial from '@/assets/icons/instagram-official.svg';
import Instagram from '@/assets/icons/instagram.svg';
import LayoutDashboard from '@/assets/icons/layout-dashboard.svg';
import LinkedinComment from '@/assets/icons/linkedin-comment.svg';
import LinkedinOfficial from '@/assets/icons/linkedin-official.svg';
import LinkedinRepost from '@/assets/icons/linkedin-repost.svg';
import LinkedinSend from '@/assets/icons/linkedin-send.svg';
import LinkedinThumbsUp from '@/assets/icons/linkedin-thumbs-up.svg';
import Linkedin from '@/assets/icons/linkedin.svg';
import ListGrid from '@/assets/icons/list-grid.svg';
import List from '@/assets/icons/list.svg';
import LoginIcon from '@/assets/icons/login.svg';
import MagicStick from '@/assets/icons/magic-stick.svg';
import Megaphone from '@/assets/icons/megaphone.svg';
import MicrosoftOfficial from '@/assets/icons/microsoft-official.svg';
import Pdf from '@/assets/icons/pdf.svg';
import PenEdit from '@/assets/icons/pen-edit.svg';
import PenNew from '@/assets/icons/pen-new.svg';
import Plus from '@/assets/icons/plus.svg';
import RefreshSquare from '@/assets/icons/refresh-square.svg';
import SharePoint from '@/assets/icons/share-point.svg';
import Site from '@/assets/icons/site.svg';
import Sort from '@/assets/icons/sort.svg';
import Sparkles from '@/assets/icons/sparkles.svg';
import Star from '@/assets/icons/star.svg';
import Swap from '@/assets/icons/swap.svg';
import Table from '@/assets/icons/table.svg';
import Trash from '@/assets/icons/trash.svg';
import UploadFile from '@/assets/icons/upload-file.svg';
import XBookmark from '@/assets/icons/x-bookmark.svg';
import XLike from '@/assets/icons/x-like.svg';
import XReply from '@/assets/icons/x-reply.svg';
import XRepost from '@/assets/icons/x-repost.svg';
import XShare from '@/assets/icons/x-share.svg';
import XView from '@/assets/icons/x-view.svg';
import LogoDark from '@/assets/logos/logo-dark.svg';
import LogoLight from '@/assets/logos/logo-light.svg';

type IconProps = React.SVGAttributes<SVGElement>;

export const Icons = {
  logoDark: (props: IconProps) => <LogoDark {...props} />,
  logoLight: (props: IconProps) => <LogoLight {...props} />,
  googleOfficial: (props: IconProps) => <GoogleOfficial {...props} />,
  microsoftOfficial: (props: IconProps) => <MicrosoftOfficial {...props} />,
  facebookOfficial: (props: IconProps) => <FacebookOfficial {...props} />,
  instagramOfficial: (props: IconProps) => <InstagramOfficial {...props} />,
  linkedinOfficial: (props: IconProps) => <LinkedinOfficial {...props} />,
  linkedin: (props: IconProps) => <Linkedin {...props} />,
  instagram: (props: IconProps) => <Instagram {...props} />,
  facebook: (props: IconProps) => <Facebook {...props} />,
  x: (props: IconProps) => <X {...props} />,
  megaphone: (props: IconProps) => <Megaphone {...props} />,
  calendar: (props: IconProps) => <Calendar {...props} />,
  calendarInput: (props: IconProps) => <CalendarInput {...props} />,
  galleryHorizontal: (props: IconProps) => <GalleryHorizontal {...props} />,
  folderOpen: (props: IconProps) => <FolderOpen {...props} />,
  crown: (props: IconProps) => <Crown {...props} />,
  headset: (props: IconProps) => <Headset {...props} />,
  layoutDashboard: (props: IconProps) => <LayoutDashboard {...props} />,
  loginIcon: (props: IconProps) => <LoginIcon {...props} />,
  circlePlay: (props: IconProps) => <CirclePlay {...props} />,
  sparkles: (props: IconProps) => <Sparkles {...props} />,
  eye: (props: IconProps) => <Eye {...props} />,
  trash: (props: IconProps) => <Trash {...props} />,
  star: (props: IconProps) => <Star {...props} />,
  filter: (props: IconProps) => <Filter {...props} />,
  sort: (props: IconProps) => <Sort {...props} />,
  list: (props: IconProps) => <List {...props} />,
  listGrid: (props: IconProps) => <ListGrid {...props} />,
  uploadFile: (props: IconProps) => <UploadFile {...props} />,
  cloudCheck: (props: IconProps) => <CloudCheck {...props} />,
  edit: (props: IconProps) => <Edit {...props} />,
  infoCircle: (props: IconProps) => <InfoCircle {...props} />,
  googleDrive: (props: IconProps) => <GoogleDrive {...props} />,
  sharePoint: (props: IconProps) => <SharePoint {...props} />,
  airtable: (props: IconProps) => <Airtable {...props} />,
  pdf: (props: IconProps) => <Pdf {...props} />,
  site: (props: IconProps) => <Site {...props} />,
  drive: (props: IconProps) => <Drive {...props} />,
  base: (props: IconProps) => <Base {...props} />,
  table: (props: IconProps) => <Table {...props} />,
  emojiIcon: (props: IconProps) => <EmojiIcon {...props} />,
  hashtag: (props: IconProps) => <Hashtag {...props} />,
  magicStick: (props: IconProps) => <MagicStick {...props} />,
  swap: (props: IconProps) => <Swap {...props} />,
  bookmark: (props: IconProps) => <Bookmark {...props} />,
  cloudUpload: (props: IconProps) => <CloudUpload {...props} />,
  calendarAdd: (props: IconProps) => <CalendarAdd {...props} />,
  galleryEdit: (props: IconProps) => <GalleryEdit {...props} />,
  penNew: (props: IconProps) => <PenNew {...props} />,
  refreshSquare: (props: IconProps) => <RefreshSquare {...props} />,
  facebookChat: (props: IconProps) => <FacebookChat {...props} />,
  facebookLike: (props: IconProps) => <FacebookLike {...props} />,
  facebookShare: (props: IconProps) => <FacebookShare {...props} />,
  linkedinThumbsUp: (props: IconProps) => <LinkedinThumbsUp {...props} />,
  linkedinSend: (props: IconProps) => <LinkedinSend {...props} />,
  linkedinComment: (props: IconProps) => <LinkedinComment {...props} />,
  linkedinRepost: (props: IconProps) => <LinkedinRepost {...props} />,
  xReply: (props: IconProps) => <XReply {...props} />,
  xLike: (props: IconProps) => <XLike {...props} />,
  xRepost: (props: IconProps) => <XRepost {...props} />,
  xView: (props: IconProps) => <XView {...props} />,
  xShare: (props: IconProps) => <XShare {...props} />,
  xBookmark: (props: IconProps) => <XBookmark {...props} />,
  penEdit: (props: IconProps) => <PenEdit {...props} />,
  arrowRight: (props: IconProps) => <ArrowRight {...props} />,
  globalScale: (props: IconProps) => <GlobalScale {...props} />,
  heart: (props: IconProps) => <Heart {...props} />,
  plus: (props: IconProps) => <Plus {...props} />,
};
