'use client';

import { createFormHook, createFormHookContexts } from '@tanstack/react-form';

import { CheckboxField } from '@/components/shared/form/checkbox-field';
import { DualSliderField } from '@/components/shared/form/dual-slider-field';
import { FileUploadField } from '@/components/shared/form/file-upload-field';
import { RadioGroupField } from '@/components/shared/form/radio-group-field';
import { SelectField } from '@/components/shared/form/select-field';
import { SliderWithInputField } from '@/components/shared/form/slider-with-input-field';
import { SubmitButton } from '@/components/shared/form/submit-button';
import { TabsField } from '@/components/shared/form/tabs-field';
import { TextField } from '@/components/shared/form/text-field';
import { TextFieldWithInnerTags } from '@/components/shared/form/text-field-with-inner-tags';
import { TextareaField } from '@/components/shared/form/textarea-field';

export const { fieldContext, useFieldContext, formContext, useFormContext } =
  createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    TextField,
    TabsField,
    RadioGroupField,
    TextFieldWithInnerTags,
    TextareaField,
    SliderWithInputField,
    CheckboxField,
    FileUploadField,
    SelectField,
    DualSliderField,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
});
