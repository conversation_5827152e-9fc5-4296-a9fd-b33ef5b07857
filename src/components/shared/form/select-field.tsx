'use client';

import React from 'react';

import { Ban } from 'lucide-react';

import { cn } from '@/lib/utils';

import { FieldErrors } from '@/components/shared/form/field-errors';
import { useFieldContext } from '@/components/shared/form/index';
import { Icons } from '@/components/shared/icons';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectItem,
  SelectValue,
  SelectContent,
  SelectTrigger,
} from '@/components/ui/select';

type SelectOption = {
  value: string;
  label?: string;
  icon?: React.ReactNode;
};

interface SelectFieldProps extends React.HTMLProps<HTMLSelectElement> {
  label?: string;
  labelClassName?: string;
  options: SelectOption[];
  showOptionLabel?: boolean;
  showOptionIcon?: boolean;
  showOptionDelete?: boolean;
  showSiteIcon?: boolean;
  showDriveIcon?: boolean;
  showBaseIcon?: boolean;
  showTableIcon?: boolean;
  onDeleteOption?: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  defaultValue?: string;
  hideTooltip?: boolean;
  isLoading?: boolean;
  onItemSelect?: (value: string) => void;
  emptyDataMessage?: string;
}

export const SelectField = ({
  label,
  labelClassName,
  options,
  showOptionLabel = true,
  showOptionIcon = false,
  showOptionDelete = false,
  showSiteIcon = false,
  showDriveIcon = false,
  showBaseIcon = false,
  showTableIcon = false,
  onDeleteOption,
  hideTooltip = true,
  required = true,
  isLoading,
  disabled,
  emptyDataMessage = 'No options available',
  onItemSelect,
  ...props
}: SelectFieldProps) => {
  const field = useFieldContext<string>();

  return (
    <div className='space-y-2'>
      <div className='grid gap-2'>
        {label && (
          <Label htmlFor={field.name} className={cn(labelClassName)}>
            {label}
            {required && <span className='ms-0.5 text-destructive'>*</span>}
          </Label>
        )}
        <Select
          value={field.state.value}
          onValueChange={(value) => {
            if (!disabled) {
              field.handleChange(value);
              if (onItemSelect) {
                onItemSelect(value);
              }
            }
          }}
          disabled={disabled}
        >
          <SelectTrigger
            id={field.name}
            onBlur={field.handleBlur}
            className={cn(
              'overflow-hidden [&>span>span]:truncate [&>span>span]:text-nowrap [&>span]:flex [&>span]:items-center [&>span]:gap-2',
              props.className
            )}
            isLoading={isLoading}
          >
            <SelectValue placeholder={props.placeholder} />
          </SelectTrigger>

          <SelectContent className='[&_*[role=option]>span>svg]:shrink-0 [&_*[role=option]>span>svg]:text-muted-foreground/80 [&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:flex [&_*[role=option]>span]:items-center [&_*[role=option]>span]:gap-2 [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2'>
            {options.length === 0 ? (
              <p className='py-4 text-center text-xs'>{emptyDataMessage}</p>
            ) : (
              options.map((option) => (
                <div key={option.value} className='group relative'>
                  <SelectItem
                    value={option.value}
                    className='[&>span]:flex [&>span]:items-center'
                    tooltip={!hideTooltip ? option.label : undefined}
                  >
                    <div className='flex w-full items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        {showSiteIcon ? (
                          <div className='text-primary'>
                            <Icons.site />
                          </div>
                        ) : showDriveIcon ? (
                          <div className='text-primary'>
                            <Icons.drive />
                          </div>
                        ) : showBaseIcon ? (
                          <div className='text-primary'>
                            <Icons.base />
                          </div>
                        ) : showTableIcon ? (
                          <div className='text-primary'>
                            <Icons.table />
                          </div>
                        ) : null}

                        {showOptionIcon && (
                          <span>{option.icon ?? <Ban />}</span>
                        )}
                        {showOptionLabel && <span>{option.label}</span>}
                      </div>
                    </div>
                  </SelectItem>
                  {showOptionDelete && onDeleteOption && (
                    <Button
                      variant='ghost'
                      size='icon'
                      className='absolute right-6 top-1/2 -translate-y-1/2 text-destructive opacity-0 transition-opacity hover:text-destructive group-hover:opacity-100'
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        onDeleteOption(option.value);
                      }}
                    >
                      <Icons.trash />
                    </Button>
                  )}
                </div>
              ))
            )}
          </SelectContent>
        </Select>
      </div>
      <FieldErrors meta={field.state.meta} />
    </div>
  );
};
