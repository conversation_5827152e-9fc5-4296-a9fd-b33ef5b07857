import React, { Fragment, useId, useState } from 'react';

import { CheckIcon, ChevronDownIcon, XIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export interface MultiSelectOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

export interface MultiSelectGroup {
  groupLabel: string;
  items: MultiSelectOption[];
}

export interface MultiSelectProps {
  /**
   * The label for the field
   */
  label?: string;
  
  /**
   * Placeholder text when no options are selected
   */
  placeholder?: string;
  
  /**
   * Search placeholder text
   */
  searchPlaceholder?: string;
  
  /**
   * The options to display, can be grouped or flat
   */
  options: MultiSelectGroup[] | MultiSelectOption[];
  
  /**
   * Currently selected values
   */
  value?: string[];
  
  /**
   * Callback when selection changes
   */
  onValueChange?: (values: string[]) => void;
  
  /**
   * Maximum number of badges to show before showing "+X more"
   */
  maxDisplayCount?: number;
  
  /**
   * Whether the field is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether the field is required
   */
  required?: boolean;
  
  /**
   * Custom className for the trigger button
   */
  className?: string;
  
  /**
   * Custom className for the popover content
   */
  popoverClassName?: string;
  
  /**
   * Whether to show the clear all button
   */
  showClearAll?: boolean;
  
  /**
   * Text for the clear all button
   */
  clearAllText?: string;
  
  /**
   * Text shown when no options are found
   */
  emptyText?: string;
}

export function MultiSelect({
  label,
  placeholder = 'Select options',
  searchPlaceholder = 'Search options...',
  options,
  value = [],
  onValueChange,
  maxDisplayCount = 3,
  disabled = false,
  required = false,
  className,
  popoverClassName,
  showClearAll = true,
  clearAllText = 'Clear all selections',
  emptyText = 'No option found.',
}: MultiSelectProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  // Normalize options to always be grouped format
  const normalizedOptions: MultiSelectGroup[] = Array.isArray(options) && 'groupLabel' in options[0]
    ? options as MultiSelectGroup[]
    : [{ groupLabel: '', items: options as MultiSelectOption[] }];

  // Flatten all options for easy lookup
  const allOptions = normalizedOptions.flatMap((group) => group.items);

  const handleValueChange = (newValues: string[]) => {
    onValueChange?.(newValues);
  };

  const handleSelect = (selectedValue: string) => {
    const newValues = value.includes(selectedValue)
      ? value.filter((val) => val !== selectedValue)
      : [...value, selectedValue];
    
    handleValueChange(newValues);
  };

  const handleRemove = (valueToRemove: string) => {
    const newValues = value.filter((val) => val !== valueToRemove);
    handleValueChange(newValues);
  };

  const handleClearAll = () => {
    handleValueChange([]);
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={id} className={cn(required && "after:content-['*'] after:ml-0.5 after:text-destructive")}>
          {label}
        </Label>
      )}
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className={cn(
              'h-auto w-full justify-between border-input bg-background px-3 font-normal outline-none outline-offset-0 hover:bg-background focus-visible:outline-[3px]',
              className
            )}
          >
            {value.length > 0 ? (
              <div className="flex min-w-0 flex-1 flex-wrap items-center gap-1">
                {value.slice(0, maxDisplayCount).map((selectedValue) => {
                  const item = allOptions.find((item) => item.value === selectedValue);
                  return (
                    <Badge
                      key={selectedValue}
                      variant="secondary"
                      className="flex items-center gap-1 text-xs"
                    >
                      {item?.icon && (
                        <span className="text-sm leading-none">
                          {item.icon}
                        </span>
                      )}
                      <span className="max-w-20 truncate">
                        {item?.label}
                      </span>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemove(selectedValue);
                        }}
                        className="ml-1 rounded-full p-0.5 hover:bg-muted-foreground/20"
                        disabled={disabled}
                      >
                        <XIcon size={12} />
                      </button>
                    </Badge>
                  );
                })}
                {value.length > maxDisplayCount && (
                  <Badge variant="outline" className="text-xs">
                    +{value.length - maxDisplayCount} more
                  </Badge>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">
                {placeholder}
              </span>
            )}
            <ChevronDownIcon
              size={16}
              className="shrink-0 text-muted-foreground/80"
              aria-hidden="true"
            />
          </Button>
        </PopoverTrigger>
        
        <PopoverContent
          className={cn(
            'w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0',
            popoverClassName
          )}
          align="start"
        >
          <Command>
            <CommandInput placeholder={searchPlaceholder} />
            
            <CommandList>
              <CommandEmpty>{emptyText}</CommandEmpty>
              
              {normalizedOptions.map((group) => (
                <Fragment key={group.groupLabel}>
                  {group.groupLabel && (
                    <CommandGroup heading={group.groupLabel}>
                      {group.items.map((item) => (
                        <CommandItem
                          key={item.value}
                          value={item.value}
                          onSelect={() => handleSelect(item.value)}
                        >
                          {item.icon && (
                            <span className="text-lg leading-none mr-2">
                              {item.icon}
                            </span>
                          )}
                          {item.label}
                          {value.includes(item.value) && (
                            <CheckIcon
                              size={16}
                              className="ml-auto"
                            />
                          )}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                  
                  {!group.groupLabel && group.items.map((item) => (
                    <CommandItem
                      key={item.value}
                      value={item.value}
                      onSelect={() => handleSelect(item.value)}
                    >
                      {item.icon && (
                        <span className="text-lg leading-none mr-2">
                          {item.icon}
                        </span>
                      )}
                      {item.label}
                      {value.includes(item.value) && (
                        <CheckIcon
                          size={16}
                          className="ml-auto"
                        />
                      )}
                    </CommandItem>
                  ))}
                </Fragment>
              ))}
            </CommandList>

            {showClearAll && value.length > 0 && (
              <CommandItem asChild className="px-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  disabled={disabled}
                >
                  {clearAllText}
                </Button>
              </CommandItem>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
