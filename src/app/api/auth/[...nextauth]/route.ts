import { User, Account, Profile } from 'next-auth';
import { AdapterUser } from 'next-auth/adapters';
import NextAuth from 'next-auth/next';
import AzureADProvider from 'next-auth/providers/azure-ad';
import GoogleProvider from 'next-auth/providers/google';
import LinkedInProvider from 'next-auth/providers/linkedin';

import { platformRegister } from '@/app/api/auth/lib/auth';

const handler = NextAuth({
  session: {
    strategy: 'jwt',
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          prompt: 'select_account',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),

    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
      authorization: {
        params: {
          // Note: Use OAuth 2.0 scopes for LinkedIn
          scope: 'r_basicprofile email r_basicprofile',
        },
      },
    }),

    AzureADProvider({
      id: 'microsoft',
      clientId: process.env.MICROSOFT_CLIENT_ID,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
      // Note: I've commented out the tenantId
      //  because I want to be able to login with any Microsoft account not only with the name@kwore.
      // tenantId: process.env.MICROSOFT_TENANT_ID,
      authorization: {
        params: {
          scope: 'openid profile email',
        },
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.id = user.id;
        token.companyId = user.companyId;
        token.brandId = user.brandId;
        token.authToken = user.token;
        token.email = user.email;
        token.name = user.name;
      }

      if (trigger === 'update') {
        return { ...token, ...session.user };
      }

      return token;
    },
    async session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          id: token.id,
          companyId: token.companyId,
          brandId: token.brandId,
          token: token.authToken,
          email: token.email,
          name: token.name,
        },
      };
    },
    async signIn({
      user,
      account,
      profile,
    }: {
      user: User | AdapterUser;
      account: Account | null;
      profile?: Profile;
    }) {
      try {
        switch (account?.provider) {
          case 'google': {
            const fetchedUser = await platformRegister({
              platform: 'google',
              email: user.email,
              username: user.name,
              profileSubId: profile?.sub,
            });

            Object.assign(user, fetchedUser);
            break;
          }

          case 'linkedin': {
            const linkedinProfile = profile as Record<string, unknown>;
            const firstName = linkedinProfile['localizedFirstName'] as string;
            const lastName = linkedinProfile['localizedLastName'] as string;

            const username = firstName + lastName;
            const email = `${firstName}.${lastName}@linkedin`;

            const fetchedUser = await platformRegister({
              platform: 'linkedin',
              email: email,
              username: username,
              profileSubId: linkedinProfile['id'] as string,
            });

            Object.assign(user, fetchedUser);
            break;
          }

          case 'microsoft': {
            const fetchedUser = await platformRegister({
              platform: 'microsoft',
              email: user.email,
              username: user.name,
              profileSubId: profile?.sub,
            });

            Object.assign(user, fetchedUser);
            break;
          }
        }

        return true;
      } catch (error) {
        console.error('Sign-in error:', error);
        return false;
      }
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/welcome',
  },
  secret: process.env.JWT_SECRET,
});

export { handler as GET, handler as POST };
