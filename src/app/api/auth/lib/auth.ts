import { User } from 'next-auth';

interface PlatformRegisterParams {
  platform: string;
  email: string;
  username?: string;
  profileSubId?: string;
}

export const platformRegister = async ({
  platform,
  email,
  username,
  profileSubId,
}: PlatformRegisterParams): Promise<User> => {
  const baseUrl = process.env.NEXT_PUBLIC_BACKEND_SERVER_URL;
  if (!baseUrl) {
    throw new Error('Backend server URL is not defined.');
  }

  const url = `${baseUrl}auth/register/platform`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      Platform: platform,
      UserEmail: email,
      ProfileSubId: profileSubId,
      UserName: username,
    }),
  });

  const data = await response.json();

  if (!response.ok) {
    const errorMessage =
      data?.error || 'An error occurred during platform registration.';
    throw new Error(errorMessage);
  }

  return data;
};
