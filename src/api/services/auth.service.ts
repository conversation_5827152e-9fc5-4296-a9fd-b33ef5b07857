import { AuthResponse } from '@/api/models/dtos/auth.dto';

import { apiPost } from '@/api/config/client';
import { ENDPOINTS } from '@/api/config/endpoints';

export const authService = {
  async login(UserEmail: string, UserPassword: string) {
    return apiPost<AuthResponse>({
      endpoint: ENDPOINTS.AUTH.LOGIN,
      payload: {
        UserEmail,
        UserPassword,
      },
    });
  },

  async register(UserName: string, UserEmail: string, UserPassword: string) {
    return apiPost<AuthResponse>({
      endpoint: ENDPOINTS.AUTH.REGISTER,
      payload: {
        UserName,
        UserEmail,
        UserPassword,
      },
    });
  },
};
