import { PlatformType } from '@/api/models/dtos/social-media-insights.dto';

import { PrepareTextModelSchema } from '@/api/models/schemas/private-model.schema';

export const ENDPOINTS = {
  AUTH: {
    LOGIN: 'auth/login',
    REGISTER: 'auth/register',
    LOGOUT: 'auth/logout',
  },
  USERS: {
    BY_EMAIL: (email: string) => `user/${email}`,
  },
  BRANDS: {
    ALL_BY_COMPANY_ID: (companyId: string) => `brands/${companyId}`,
    BY_ID: (brandId: string) => `brand/${brandId}`,
  },
  SOCIAL_MEDIA: {
    INSIGHTS_BY_PLATFORM: (platform: PlatformType) => `${platform}/getinsights`,
  },
  POSTS: {
    CREATE_NEW_SUGGESTIONS: 'form/create',
    SAVE_SUGGESTIONS_BY_ID: (postId: string) => `content/save/${postId}`,
    BY_ID: (postId: string) => `content/${postId}`,
    SAVED_SUGGESTIONS_BY_ID: (postId: string) => `content/saved/${postId}`,
    BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `contents/company/${companyId}/${brandId}`,
    GET_GENERATED_POST_PROMPT: 'form/gen_prompt',
    UPDATE_GENERATED_POST_PROMPT: 'form/process_prompt',
    UPDATE: 'form/edittext',
    UPDATE_CONTENT: 'content/update',
    MARK_FAVORITE: (postId: string) => `content/favorite/${postId}`,
    DELETE: (postId: string) => `content/delete/${postId}`,
  },
  AI_MODELS: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `ai/models/company/${companyId}/${brandId}`,
    BY_ID: (id: string) => `ai/model/${id}`,
    CHECK_LIMIT: 'ai/models/checklimit',
    PREPARE_TEXT_PRIVATE_MODEL: (
      platformName?: PrepareTextModelSchema['SocialMediaData'][number]['platform']
    ) => `social-media-scraper/${platformName}`,
    CREATE_TEXT_PRIVATE_MODEL: 'ai/model/create/llm',
    CREATE_DUMMY_TEXT_PRIVATE_MODEL: 'dummy/ai/model/create/llm',
    PREPARE_IMAGE_PRIVATE_MODEL: 'ai/model/preparedata/image',
    CREATE_IMAGE_PRIVATE_MODEL: 'ai/model/create/image',
    MARK_FAVORITE: (id: string) => `ai/models/favorite/${id}`,
    DELETE: (id: string) => `ai/model/delete/${id}`,
  },
  KNOWLEDGE: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `knowledges/${companyId}/${brandId}`,
    MARK_FAVORITE: (id: string) => `knowledge/favorite/${id}`,
    DELETE: (id: string) => `knowledge/delete/${id}`,
    CREATE_KNOWLEDGE: 'knowledge/create',
    UPDATE_KNOWLEDGE: 'knowledge/update',
    GET_WEBSITE_URL_SCRAPER: 'site-data',

    GOOGLE_AUTH: 'google/connect',
    ALL_GOOGLE_DRIVE_ACCOUNTS: 'google/accounts',
    GOOGLE_DRIVE_FILES: 'google/files',
    GOOGLE_SELECT_FILE: 'google/file/select',
    GOOGLE_REVOKE: 'google/revoke',

    SHAREPOINT_AUTH: 'sharepoint/connect',
    SHAREPOINT_ACCOUNTS: 'sharepoint/accounts',
    SHAREPOINT_SITES: 'sharepoint/sites',
    SHAREPOINT_DRIVES: 'sharepoint/drives',
    SHAREPOINT_FOLDERS: 'sharepoint/folders',
    SHAREPOINT_FILES: 'sharepoint/files',
    SHAREPOINT_SELECT_FILE: 'sharepoint/file/select',
    SHAREPOINT_REVOKE: 'sharepoint/revoke',

    AIRTABLE_AUTH: 'airtable/connect',
    AIRTABLE_ACCOUNTS: 'airtable/accounts',
    AIRTABLE_BASES: 'airtable/bases',
    AIRTABLE_TABLES: 'airtable/tables',
    AIRTABLE_SELECT_TABLE: 'airtable/table/select',
    AIRTABLE_REVOKE: 'airtable/revoke',
  },
  CAMPAIGNS: {
    ALL_BY_COMPANY_BRAND_ID: (companyId: string, brandId: string) =>
      `campaign/company/${companyId}/${brandId}`,
    BY_ID: (campaignId: string) => `campaign/${campaignId}`,
  },
  // Add more endpoints as needed
};
