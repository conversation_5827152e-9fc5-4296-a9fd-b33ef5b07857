# Stage 1: Build
FROM node:22.14.0-alpine AS builder
LABEL name="kwore-image"
WORKDIR /app

ARG NEXT_PUBLIC_BACKEND_SERVER_URL
ARG NEXT_PUBLIC_BACKEND_NEXT_SERVER_URL
ARG ENV

ENV NEXT_PUBLIC_BACKEND_SERVER_URL=$NEXT_PUBLIC_BACKEND_SERVER_URL
ENV NEXT_PUBLIC_BACKEND_NEXT_SERVER_URL=$NEXT_PUBLIC_BACKEND_NEXT_SERVER_URL
ENV ENV=$ENV

RUN npm install -g pnpm@10.3.0

COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

COPY . .
RUN pnpm run build

# Stage 2: Run
FROM node:22.14.0-alpine AS runner
LABEL name="kwore-app"
WORKDIR /app

ARG NODE_ENV=production
ENV NODE_ENV=$NODE_ENV

RUN npm install -g pnpm@10.3.0

COPY --from=builder /app ./

EXPOSE 3000
CMD ["pnpm", "start"]
